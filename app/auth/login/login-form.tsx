"use client";

import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { Lock, Mail, Eye, EyeOff } from "lucide-react";
import Image from "next/image";

type AuthMode = "login" | "signup";

export function LoginForm({
  className,
  ...props
}: React.ComponentPropsWithoutRef<"div">) {
  const [mode, setMode] = useState<AuthMode>("login");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [repeatPassword, setRepeatPassword] = useState("");
  const [rememberMe, setRememberMe] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showRepeatPassword, setShowRepeatPassword] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();


  const checkEmailExists = async (email: string): Promise<boolean> => {
    try {
      const response = await fetch('/api/check-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      console.log('Client: Response status:', response.status);
      console.log('Client: Response headers:', Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Client: API error response:', errorText);
        throw new Error(`API error: ${response.status} - ${errorText}`);
      }

      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        const responseText = await response.text();
        console.error('Client: Non-JSON response:', responseText);
        throw new Error('API returned non-JSON response');
      }

      const data = await response.json();
      console.log('Client: API response data:', data);
      return data.exists;
    } catch (error) {
      console.error('Client: Error checking email, falling back to dummy password method:', error);

      // If API fails, return false to allow signup
      console.error('Client: Check email API failed, allowing signup');
      return false;
    }
  };

  const handleAuth = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      if (mode === "login") {
        const response = await fetch('/api/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ email, password }),
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || 'Login failed');
        }

        router.push("/");
      } else if (mode === "signup") {
        if (password !== repeatPassword) {
          setError("Passwords do not match");
          setIsLoading(false);
          return;
        }

        // Check if email already exists before attempting signup
        const emailExists = await checkEmailExists(email);

        if (emailExists) {
          setError("This email is already registered. Please switch to Login mode or use a different email.");
          setIsLoading(false);
          return;
        }

        // Proceed with signup if email doesn't exist
        const response = await fetch('/api/auth/signup', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ email, password }),
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || 'Sign up failed');
        }

        router.push("/auth/sign-up-success");
      }
    } catch (error: unknown) {
      setError(error instanceof Error ? error.message : "An error occurred");
    } finally {
      setIsLoading(false);
    }
  };


  const handleGoogleLogin = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/auth/oauth', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ provider: 'google', next: '/' }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'OAuth login failed');
      }

      // Redirect to the OAuth URL
      if (data.url) {
        window.location.href = data.url;
      }
    } catch (error: unknown) {
      setError(error instanceof Error ? error.message : "An error occurred");
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    setEmail("");
    setPassword("");
    setRepeatPassword("");
    setError(null);
  };

  return (
    <div className={cn("min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4", className)} {...props}>

      {/* Login Form Card with Image */}
      <div className="w-full max-w-6xl bg-white rounded-2xl shadow-2xl overflow-hidden mt-8">
        <div className="flex min-h-[600px]">
          {/* Left Side - Form */}
          <div className="flex-1 p-8 flex flex-col justify-center">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-gray-800 mb-2">
                {mode === "login" ? "LOG IN" : "SIGN UP"}
              </h1>
              <p className="text-gray-600 text-sm">
                {mode === "login"
                  ? "Welcome back! Please sign in to your account"
                  : "Create your account to get started"
                }
              </p>
            </div>

            {/* Mode Toggle */}
            <div className="flex mb-6 bg-gray-100 rounded-lg p-1">
              <button
                type="button"
                onClick={() => { setMode("login"); resetForm(); }}
                className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${mode === "login"
                  ? "bg-white text-gray-900 shadow-sm"
                  : "text-gray-600 hover:text-gray-900"
                  }`}
              >
                Login
              </button>
              <button
                type="button"
                onClick={() => { setMode("signup"); resetForm(); }}
                className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${mode === "signup"
                  ? "bg-white text-gray-900 shadow-sm"
                  : "text-gray-600 hover:text-gray-900"
                  }`}
              >
                Sign Up
              </button>
            </div>

            <form onSubmit={handleAuth} className="space-y-6">
              {/* Email Field */}
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Mail className="h-5 w-5 text-gray-400" />
                </div>
                <Input
                  type="email"
                  placeholder="email address"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="pl-10 h-12 text-lg"
                  required
                />
              </div>

              {/* Password Field */}
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Lock className="h-5 w-5 text-gray-400" />
                </div>
                <Input
                  type={showPassword ? "text" : "password"}
                  placeholder="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="pl-10 pr-10 h-12 text-lg"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                >
                  {showPassword ? (
                    <EyeOff className="h-5 w-5 text-gray-400" />
                  ) : (
                    <Eye className="h-5 w-5 text-gray-400" />
                  )}
                </button>
              </div>

              {/* Repeat Password Field - Only show in signup mode */}
              {mode === "signup" && (
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Lock className="h-5 w-5 text-gray-400" />
                  </div>
                  <Input
                    type={showRepeatPassword ? "text" : "password"}
                    placeholder="repeat password"
                    value={repeatPassword}
                    onChange={(e) => setRepeatPassword(e.target.value)}
                    className="pl-10 pr-10 h-12 text-lg"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowRepeatPassword(!showRepeatPassword)}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  >
                    {showRepeatPassword ? (
                      <EyeOff className="h-5 w-5 text-gray-400" />
                    ) : (
                      <Eye className="h-5 w-5 text-gray-400" />
                    )}
                  </button>
                </div>
              )}

              {/* Remember Me & Forgot Password - Only show in login mode */}
              {mode === "login" && (
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="remember"
                      checked={rememberMe}
                      onCheckedChange={(checked) => setRememberMe(checked as boolean)}
                    />
                    <Label htmlFor="remember" className="text-sm text-gray-600">
                      Remember me
                    </Label>
                  </div>
                  <Link
                    href="/auth/forgot-password"
                    className="text-sm text-blue-600 hover:underline"
                  >
                    Forget password?
                  </Link>
                </div>
              )}

              {/* Error Message */}
              {error && (
                <div className="text-sm text-red-500 text-center">
                  {error}
                  {error.includes("already registered") && (
                    <div className="mt-2">
                      <button
                        type="button"
                        onClick={() => setMode("login")}
                        className="text-blue-600 hover:underline font-medium"
                      >
                        Switch to Login mode →
                      </button>
                    </div>
                  )}
                </div>
              )}

              {/* Main Action Button */}
              <Button
                type="submit"
                disabled={isLoading}
                className="w-full h-12 bg-yellow-400 hover:bg-yellow-500 text-white font-semibold text-lg rounded-lg"
              >
                {isLoading
                  ? (mode === "login" ? "Logging in..." : "Creating account...")
                  : (mode === "login" ? "Login" : "Sign Up")
                }
              </Button>

              {/* Mode Switch Link */}
              <div className="text-center">
                <span className="text-gray-600">
                  {mode === "login" ? "No account? " : "Already have an account? "}
                </span>
                <button
                  type="button"
                  onClick={() => {
                    setMode(mode === "login" ? "signup" : "login");
                    resetForm();
                  }}
                  className="text-blue-600 hover:underline font-medium"
                >
                  {mode === "login" ? "Sign up" : "Login"}
                </button>
              </div>

              {/* Social Login */}
              <div className="text-center">
                <p className="text-gray-500 text-sm mb-4">or continue with</p>
                <div className="flex justify-center">
                  <button
                    type="button"
                    onClick={handleGoogleLogin}
                    disabled={isLoading}
                    className="w-12 h-12 bg-white border-2 border-gray-300 rounded-full flex items-center justify-center hover:bg-gray-50 transition-colors"
                  >
                    <svg className="w-6 h-6" viewBox="0 0 24 24">
                      <path
                        fill="#4285F4"
                        d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                      />
                      <path
                        fill="#34A853"
                        d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                      />
                      <path
                        fill="#FBBC05"
                        d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                      />
                      <path
                        fill="#EA4335"
                        d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                      />
                    </svg>
                  </button>
                </div>
              </div>

              {/* Legal Text */}
              <div className="text-xs text-gray-500 text-center leading-relaxed">
                By continuing, you agree to PawHub&apos;s Terms of Service and acknowledge that you&apos;ve read our Privacy Policy.
              </div>
            </form>
          </div>

          {/* Right Side - Image */}
          <div className="flex-1 relative">
            <Image
              src="/login-cover.webp"
              alt="Login Cover"
              fill
              className="object-cover"
              priority
            />
          </div>
        </div>
      </div>
    </div>
  );
}
