import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON> } from "next/font/google";
import { ThemeProvider } from "next-themes";
import "./globals.css";
import { Header } from "@/components/header";
import { ClientFooter } from "@/components/client-footer";

const defaultUrl = process.env.NEXT_PUBLIC_URL
  ? `https://${process.env.NEXT_PUBLIC_URL}`
  : "http://localhost:3000";

export const metadata: Metadata = {
  metadataBase: new URL(defaultUrl),
  title: "PawHub - Smarter Pet Care, Anytime",
  description: "Dịch vụ thú y được tạo ra với tình yêu thương vô điều kiện. AI-powered pet care services.",
  icons: {
    icon: "/favicon.ico",
  },
  openGraph: {
    title: "PawHub - Smarter Pet Care, Anytime",
    description: "Dịch vụ thú y được tạo ra với tình yêu thương vô điều kiện. AI-powered pet care services.",
    images: ["/opengraph-image.png"],
  },
  twitter: {
    card: "summary_large_image",
    title: "PawHub - Smarter Pet Care, Anytime",
    description: "Dịch vụ thú y được tạo ra với tình yêu thương vô điều kiện. AI-powered pet care services.",
    images: ["/twitter-image.png"],
  },
};

const geistSans = Geist({
  variable: "--font-geist-sans",
  display: "swap",
  subsets: ["latin"],
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${geistSans.className} antialiased min-h-screen flex flex-col`}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <Header />
          <main className="flex-1">
            {children}
          </main>
          <ClientFooter />
        </ThemeProvider>
      </body>
    </html>
  );
}
